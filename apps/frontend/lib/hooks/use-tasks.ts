import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { tasksAPI } from '@/lib/api';
import { useTaskStore } from '@/lib/stores/app-store';
import { toast } from 'sonner';
import type { Task } from '@mtbrmg/shared';

// Query keys
export const taskKeys = {
  all: ['tasks'] as const,
  lists: () => [...taskKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...taskKeys.lists(), filters] as const,
  details: () => [...taskKeys.all, 'detail'] as const,
  detail: (id: string) => [...taskKeys.details(), id] as const,
  stats: () => [...taskKeys.all, 'stats'] as const,
  byProject: (projectId: string) => [...taskKeys.all, 'project', projectId] as const,
  byAssignee: (assigneeId: string) => [...taskKeys.all, 'assignee', assigneeId] as const,
};

// Hooks for tasks data
export function useTasks(params?: Record<string, any>) {
  const { setTasks, setLoading, setError } = useTaskStore();

  return useQuery({
    queryKey: taskKeys.list(params || {}),
    queryFn: () => tasksAPI.getTasks(params),
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 8 * 60 * 1000, // 8 minutes
    onSuccess: (data) => {
      setTasks(data.results || data);
      setLoading(false);
      setError(null);
    },
    onError: (error: any) => {
      setError(error.message || 'فشل في جلب بيانات المهام');
      setLoading(false);
    },
  });
}

export function useTask(id: string) {
  return useQuery({
    queryKey: taskKeys.detail(id),
    queryFn: () => tasksAPI.getTask(id),
    enabled: !!id,
    staleTime: 3 * 60 * 1000,
    gcTime: 8 * 60 * 1000,
  });
}

export function useTasksByProject(projectId: string) {
  return useQuery({
    queryKey: taskKeys.byProject(projectId),
    queryFn: () => tasksAPI.getTasks({ project: projectId }),
    enabled: !!projectId,
    staleTime: 3 * 60 * 1000,
    gcTime: 8 * 60 * 1000,
  });
}

export function useTasksByAssignee(assigneeId: string) {
  return useQuery({
    queryKey: taskKeys.byAssignee(assigneeId),
    queryFn: () => tasksAPI.getTasks({ assigned_to: assigneeId }),
    enabled: !!assigneeId,
    staleTime: 3 * 60 * 1000,
    gcTime: 8 * 60 * 1000,
  });
}

export function useTaskStats() {
  return useQuery({
    queryKey: taskKeys.stats(),
    queryFn: () => tasksAPI.getTasks({ stats: true }),
    staleTime: 2 * 60 * 1000, // 2 minutes for stats
    gcTime: 5 * 60 * 1000,
  });
}

// Mutations for task operations
export function useCreateTask() {
  const queryClient = useQueryClient();
  const { addTask } = useTaskStore();

  return useMutation({
    mutationFn: tasksAPI.createTask,
    onSuccess: (newTask) => {
      // Update the store
      addTask(newTask);
      
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: taskKeys.all });
      
      toast.success('تم إنشاء المهمة بنجاح');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'فشل في إنشاء المهمة');
    },
  });
}

export function useUpdateTask() {
  const queryClient = useQueryClient();
  const { updateTask } = useTaskStore();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Task> }) =>
      tasksAPI.updateTask(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: taskKeys.detail(id) });

      // Snapshot the previous value
      const previousTask = queryClient.getQueryData(taskKeys.detail(id));

      // Optimistically update the cache
      queryClient.setQueryData(taskKeys.detail(id), (old: any) => ({
        ...old,
        ...data,
      }));

      // Update the store optimistically
      updateTask(id, data);

      return { previousTask, id };
    },
    onError: (error: any, { id }, context) => {
      // Rollback on error
      if (context?.previousTask) {
        queryClient.setQueryData(taskKeys.detail(id), context.previousTask);
      }
      toast.error(error.response?.data?.message || 'فشل في تحديث المهمة');
    },
    onSuccess: (updatedTask, { id }) => {
      // Update the store with the server response
      updateTask(id, updatedTask);
      toast.success('تم تحديث المهمة بنجاح');
    },
    onSettled: (_, __, { id }) => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: taskKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: taskKeys.lists() });
    },
  });
}

export function useDeleteTask() {
  const queryClient = useQueryClient();
  const { removeTask } = useTaskStore();

  return useMutation({
    mutationFn: tasksAPI.deleteTask,
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: taskKeys.all });

      // Optimistically remove from store
      removeTask(id);

      return { id };
    },
    onError: (error: any, id) => {
      toast.error(error.response?.data?.message || 'فشل في حذف المهمة');
    },
    onSuccess: () => {
      toast.success('تم حذف المهمة بنجاح');
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: taskKeys.all });
    },
  });
}

// Combined hook for task management
export function useTaskManagement() {
  const queryClient = useQueryClient();
  
  const createTask = useCreateTask();
  const updateTask = useUpdateTask();
  const deleteTask = useDeleteTask();

  const invalidateTasks = () => {
    queryClient.invalidateQueries({ queryKey: taskKeys.all });
  };

  const prefetchTask = (id: string) => {
    queryClient.prefetchQuery({
      queryKey: taskKeys.detail(id),
      queryFn: () => tasksAPI.getTask(id),
      staleTime: 3 * 60 * 1000,
    });
  };

  const markTaskCompleted = useMutation({
    mutationFn: (id: string) =>
      tasksAPI.updateTask(id, { status: 'completed' }),
    onSuccess: (updatedTask) => {
      queryClient.setQueryData(
        taskKeys.detail(updatedTask.id),
        updatedTask
      );
      toast.success('تم إكمال المهمة');
    },
    onError: (error: any) => {
      toast.error('فشل في تحديث حالة المهمة');
    },
  });

  const logTime = useMutation({
    mutationFn: ({ taskId, timeData }: { taskId: string; timeData: any }) =>
      tasksAPI.logTime(taskId, timeData),
    onSuccess: () => {
      toast.success('تم تسجيل الوقت بنجاح');
      invalidateTasks();
    },
    onError: (error: any) => {
      toast.error('فشل في تسجيل الوقت');
    },
  });

  return {
    createTask,
    updateTask,
    deleteTask,
    markTaskCompleted,
    logTime,
    invalidateTasks,
    prefetchTask,
  };
}
